from aiogram.types import InlineKeyboardMarkup, InlineKeyboardButton
from common.keyboards import get_main_menu_back_button


def get_group_homework_analytics_kb(group_id: int) -> InlineKeyboardMarkup:
    """Клавиатура для просмотра статистики по группе в домашних заданиях"""
    return InlineKeyboardMarkup(inline_keyboard=[
        [InlineKeyboardButton(
            text="📈 % понимания по микротемам",
            callback_data=f"homework_group_microtopics_detailed_{group_id}"
        )],
        *get_main_menu_back_button()
    ])


def get_homework_menu_kb() -> InlineKeyboardMarkup:
    """Клавиатура меню домашних заданий"""
    return InlineKeyboardMarkup(inline_keyboard=[
        [InlineKeyboardButton(text="Статистика по ученику", callback_data="hw_student_stats")],
        [InlineKeyboardButton(text="Статистика по группе", callback_data="hw_group_stats")],
        *get_main_menu_back_button()
    ])
