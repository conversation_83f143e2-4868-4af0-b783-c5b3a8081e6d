import logging
from aiogram import F, Router
from aiogram.types import CallbackQuery
from aiogram.fsm.context import FSMContext
from .handlers import (
    show_homework_menu, select_student_stats_course, select_student_stats_group,
    select_student_stats_lesson, show_student_stats_list, select_group_stats_group,
    show_group_stats, enter_homework_message, confirm_homework_message,
    send_homework_message, cancel_homework_message
)

# Настройка логирования
logging.basicConfig(level=logging.INFO)

def register_homework_handlers(router: Router, states_group, role: str):
    """Регистрация обработчиков для работы с домашними заданиями"""

    # Универсальные обработчики пагинации для микротем групп в домашних заданиях
    @router.callback_query(F.data.startswith(f"{role}_group_microtopics_page_"))
    async def role_handle_group_microtopics_pagination(callback: CallbackQuery, state: FSMContext):
        """Универсальный обработчик пагинации микротем группы для домашних заданий"""
        logging.info(f"🔥 ПАГИНАЦИЯ ГРУППЫ ДЗ {role.upper()}: callback={callback.data}, state={await state.get_state()}")
        from common.microtopics.handlers import handle_microtopics_pagination_universal
        await handle_microtopics_pagination_universal(
            callback=callback,
            state=state,
            callback_prefix=f"{role}_group_microtopics_page",
            display_mode="detailed",
            role=role
        )

    # Универсальный обработчик возврата из изображений микротем группы (с фильтрацией по состояниям)
    @router.callback_query(states_group.group_stats_result, F.data == "back_from_microtopics_image")
    async def role_back_from_group_microtopics_image_homework(callback: CallbackQuery, state: FSMContext):
        """Обработчик возврата из изображений микротем группы в домашних заданиях для конкретной роли"""
        current_state = await state.get_state()
        logging.info(f"🔥 ВОЗВРАТ ИЗ МИКРОТЕМ ДЗ {role.upper()}: state={current_state}")

        # Получаем данные группы и показываем статистику без микротем
        data = await state.get_data()
        group_id = data.get('selected_group_id')

        if group_id:
            # Удаляем изображение и показываем обычную статистику
            try:
                await callback.message.delete()
            except:
                pass

            # Показываем статистику группы без изображений микротем
            from common.statistics import get_group_stats
            stats = await get_group_stats(str(group_id))

            # Формируем текст с рейтингом
            rating_text = ""
            if stats["rating"]:
                rating_text = "\n📋 Рейтинг по баллам:\n"
                for i, student in enumerate(stats["rating"], 1):
                    rating_text += f"{i}. {student['name']} — {student['points']} баллов\n"

            from common.keyboards import get_main_menu_back_button
            from aiogram.types import InlineKeyboardMarkup

            await callback.message.answer(
                f"👥 Группа: {stats['name']}\n"
                f"📗 {stats['subject']}\n"
                f"📊 Средний % выполнения ДЗ: {stats['homework_completion']}%\n"
                f"📈 Статистика по микротемам показана выше{rating_text}",
                reply_markup=InlineKeyboardMarkup(inline_keyboard=[
                    *get_main_menu_back_button()
                ])
            )
        else:
            # Если нет данных о группе, возвращаемся к меню домашних заданий
            await show_homework_menu(callback, state, role)
            await state.set_state(states_group.homework_menu)

    @router.callback_query(F.data == f"{role}_homeworks")
    async def show_role_homework_menu(callback: CallbackQuery, state: FSMContext):
        logging.info(f"ВЫЗОВ: show_role_homework_menu | КОЛБЭК: {callback.data} | СОСТОЯНИЕ: {await state.get_state()}")
        await show_homework_menu(callback, state, role)
        await state.set_state(states_group.homework_menu)

    @router.callback_query(states_group.homework_menu, F.data == "hw_student_stats")
    async def role_select_student_stats_course(callback: CallbackQuery, state: FSMContext):
        logging.info(f"ВЫЗОВ: role_select_student_stats_course | КОЛБЭК: {callback.data} | СОСТОЯНИЕ: {await state.get_state()}")
        await select_student_stats_course(callback, state, role)
        await state.set_state(states_group.student_stats_course)

    @router.callback_query(states_group.student_stats_course, F.data.startswith("course_"))
    async def role_select_student_stats_group(callback: CallbackQuery, state: FSMContext):
        logging.info(f"ВЫЗОВ: role_select_student_stats_group | КОЛБЭК: {callback.data} | СОСТОЯНИЕ: {await state.get_state()}")
        await select_student_stats_group(callback, state, role)
        await state.set_state(states_group.student_stats_group)

    @router.callback_query(states_group.student_stats_group, F.data.startswith("analytics_group_"))
    async def role_select_student_stats_lesson(callback: CallbackQuery, state: FSMContext):
        logging.info(f"ВЫЗОВ: role_select_student_stats_lesson | КОЛБЭК: {callback.data} | СОСТОЯНИЕ: {await state.get_state()}")
        await select_student_stats_lesson(callback, state, role)
        await state.set_state(states_group.student_stats_lesson)

    @router.callback_query(states_group.student_stats_lesson, F.data.startswith("lesson_"))
    async def role_show_student_stats_list(callback: CallbackQuery, state: FSMContext):
        logging.info(f"ВЫЗОВ: role_show_student_stats_list | КОЛБЭК: {callback.data} | СОСТОЯНИЕ: {await state.get_state()}")
        await show_student_stats_list(callback, state, role)
        await state.set_state(states_group.student_stats_list)

    @router.callback_query(states_group.homework_menu, F.data == "hw_group_stats")
    async def role_select_group_stats_group(callback: CallbackQuery, state: FSMContext):
        logging.info(f"ВЫЗОВ: role_select_group_stats_group | КОЛБЭК: {callback.data} | СОСТОЯНИЕ: {await state.get_state()}")
        await select_group_stats_group(callback, state, role)
        await state.set_state(states_group.group_stats_group)

    @router.callback_query(states_group.group_stats_group, F.data.startswith("analytics_group_"))
    async def role_show_group_stats(callback: CallbackQuery, state: FSMContext):
        logging.info(f"ВЫЗОВ: role_show_group_stats | КОЛБЭК: {callback.data} | СОСТОЯНИЕ: {await state.get_state()}")
        await show_group_stats(callback, state, role)
        await state.set_state(states_group.group_stats_result)

    # Обработчики для отправки сообщений студентам
    @router.callback_query(states_group.student_stats_list, F.data.startswith("hw_message_student_"))
    async def role_enter_homework_message(callback: CallbackQuery, state: FSMContext):
        logging.info(f"ВЫЗОВ: role_enter_homework_message | КОЛБЭК: {callback.data} | СОСТОЯНИЕ: {await state.get_state()}")
        await enter_homework_message(callback, state, role)
        await state.set_state(states_group.enter_homework_message)

    @router.message(states_group.enter_homework_message)
    async def role_confirm_homework_message(message, state: FSMContext):
        logging.info(f"ВЫЗОВ: role_confirm_homework_message | СООБЩЕНИЕ: {message.text[:50]}... | СОСТОЯНИЕ: {await state.get_state()}")
        await confirm_homework_message(message, state, role)
        await state.set_state(states_group.confirm_homework_message)

    @router.callback_query(states_group.confirm_homework_message, F.data == "send_homework_message")
    async def role_send_homework_message(callback: CallbackQuery, state: FSMContext):
        logging.info(f"ВЫЗОВ: role_send_homework_message | КОЛБЭК: {callback.data} | СОСТОЯНИЕ: {await state.get_state()}")
        await send_homework_message(callback, state, role)

    @router.callback_query(states_group.confirm_homework_message, F.data == "cancel_homework_message")
    async def role_cancel_homework_message(callback: CallbackQuery, state: FSMContext):
        logging.info(f"ВЫЗОВ: role_cancel_homework_message | КОЛБЭК: {callback.data} | СОСТОЯНИЕ: {await state.get_state()}")
        await cancel_homework_message(callback, state, role)
