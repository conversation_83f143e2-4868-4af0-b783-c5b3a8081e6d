import logging
from aiogram import F, Router
from aiogram.types import CallbackQuery
from aiogram.fsm.context import FSMContext
from .handlers import (
    show_homework_menu, select_student_stats_course, select_student_stats_group,
    select_student_stats_lesson, show_student_stats_list, select_group_stats_group,
    show_group_stats, enter_homework_message, confirm_homework_message,
    send_homework_message, cancel_homework_message
)

# Настройка логирования
logging.basicConfig(level=logging.INFO)

def register_homework_handlers(router: Router, states_group, role: str):
    """Регистрация обработчиков для работы с домашними заданиями"""

    # Обработчик для показа микротем группы в домашних заданиях
    @router.callback_query(states_group.group_stats_result, F.data.startswith("homework_group_microtopics_detailed_"))
    async def role_show_homework_group_microtopics_detailed(callback: CallbackQuery, state: FSMContext):
        """Показать детальную статистику по микротемам группы в домашних заданиях"""
        logging.info(f"🔥 МИКРОТЕМЫ ГРУППЫ ДЗ {role.upper()}: callback={callback.data}, state={await state.get_state()}")

        # Извлекаем group_id из callback_data
        # Формат: homework_group_microtopics_detailed_GROUP_ID
        parts = callback.data.split("_")
        if len(parts) >= 5:
            group_id = int(parts[4])

            # Определяем состояния и префиксы для разных ролей
            if role == "curator":
                from curator.handlers.analytics import CuratorAnalyticsStates
                target_state = CuratorAnalyticsStates.group_stats_display
                callback_prefix = "curator_group_microtopics_page"
            elif role == "teacher":
                from teacher.handlers.analytics import TeacherAnalyticsStates
                target_state = TeacherAnalyticsStates.group_stats_display
                callback_prefix = "teacher_group_microtopics_page"
            elif role == "manager":
                from manager.handlers.analytics import ManagerAnalyticsStates
                target_state = ManagerAnalyticsStates.group_stats_display
                callback_prefix = "manager_group_microtopics_page"
            else:
                target_state = None
                callback_prefix = "group_microtopics_page"

            # Сохраняем ID группы для возврата
            await state.update_data(selected_group_id=group_id)

            from common.microtopics.handlers import show_group_microtopics_universal
            from common.homework.handlers import back_from_homework_group_microtopics_to_stats

            await show_group_microtopics_universal(
                callback=callback,
                state=state,
                group_id=group_id,
                role=role,
                target_state=target_state,
                callback_prefix=callback_prefix,
                back_keyboard_func=lambda: None,
                title="👥 Статистика группы\n📈 Средний % понимания по микротемам",
                items_per_page=15,
                caption="📊 Статистика группы по микротемам",
                premium_check=False
            )

    # Универсальные обработчики пагинации для микротем групп в домашних заданиях
    @router.callback_query(F.data.startswith(f"{role}_group_microtopics_page_"))
    async def role_handle_group_microtopics_pagination(callback: CallbackQuery, state: FSMContext):
        """Универсальный обработчик пагинации микротем группы для домашних заданий"""
        logging.info(f"🔥 ПАГИНАЦИЯ ГРУППЫ ДЗ {role.upper()}: callback={callback.data}, state={await state.get_state()}")
        from common.microtopics.handlers import handle_microtopics_pagination_universal
        await handle_microtopics_pagination_universal(
            callback=callback,
            state=state,
            callback_prefix=f"{role}_group_microtopics_page",
            display_mode="detailed",
            role=role
        )

    # Универсальный обработчик возврата из изображений микротем группы
    @router.callback_query(F.data == "back_from_microtopics_image")
    async def role_back_from_group_microtopics_image_homework(callback: CallbackQuery, state: FSMContext):
        """Обработчик возврата из изображений микротем группы в домашних заданиях для конкретной роли"""
        current_state = await state.get_state()
        logging.info(f"🔥 ВОЗВРАТ ИЗ МИКРОТЕМ ДЗ {role.upper()}: state={current_state}")

        # Проверяем, что мы в состоянии аналитики (куда переходим при показе микротем)
        if "group_stats_display" in str(current_state):
            from common.homework.handlers import back_from_homework_group_microtopics_to_stats
            await back_from_homework_group_microtopics_to_stats(callback, state, role)

    @router.callback_query(F.data == f"{role}_homeworks")
    async def show_role_homework_menu(callback: CallbackQuery, state: FSMContext):
        logging.info(f"ВЫЗОВ: show_role_homework_menu | КОЛБЭК: {callback.data} | СОСТОЯНИЕ: {await state.get_state()}")
        await show_homework_menu(callback, state, role)
        await state.set_state(states_group.homework_menu)

    @router.callback_query(states_group.homework_menu, F.data == "hw_student_stats")
    async def role_select_student_stats_course(callback: CallbackQuery, state: FSMContext):
        logging.info(f"ВЫЗОВ: role_select_student_stats_course | КОЛБЭК: {callback.data} | СОСТОЯНИЕ: {await state.get_state()}")
        await select_student_stats_course(callback, state, role)
        await state.set_state(states_group.student_stats_course)

    @router.callback_query(states_group.student_stats_course, F.data.startswith("course_"))
    async def role_select_student_stats_group(callback: CallbackQuery, state: FSMContext):
        logging.info(f"ВЫЗОВ: role_select_student_stats_group | КОЛБЭК: {callback.data} | СОСТОЯНИЕ: {await state.get_state()}")
        await select_student_stats_group(callback, state, role)
        await state.set_state(states_group.student_stats_group)

    @router.callback_query(states_group.student_stats_group, F.data.startswith("analytics_group_"))
    async def role_select_student_stats_lesson(callback: CallbackQuery, state: FSMContext):
        logging.info(f"ВЫЗОВ: role_select_student_stats_lesson | КОЛБЭК: {callback.data} | СОСТОЯНИЕ: {await state.get_state()}")
        await select_student_stats_lesson(callback, state, role)
        await state.set_state(states_group.student_stats_lesson)

    @router.callback_query(states_group.student_stats_lesson, F.data.startswith("lesson_"))
    async def role_show_student_stats_list(callback: CallbackQuery, state: FSMContext):
        logging.info(f"ВЫЗОВ: role_show_student_stats_list | КОЛБЭК: {callback.data} | СОСТОЯНИЕ: {await state.get_state()}")
        await show_student_stats_list(callback, state, role)
        await state.set_state(states_group.student_stats_list)

    @router.callback_query(states_group.homework_menu, F.data == "hw_group_stats")
    async def role_select_group_stats_group(callback: CallbackQuery, state: FSMContext):
        logging.info(f"ВЫЗОВ: role_select_group_stats_group | КОЛБЭК: {callback.data} | СОСТОЯНИЕ: {await state.get_state()}")
        await select_group_stats_group(callback, state, role)
        await state.set_state(states_group.group_stats_group)

    @router.callback_query(states_group.group_stats_group, F.data.startswith("analytics_group_"))
    async def role_show_group_stats(callback: CallbackQuery, state: FSMContext):
        logging.info(f"ВЫЗОВ: role_show_group_stats | КОЛБЭК: {callback.data} | СОСТОЯНИЕ: {await state.get_state()}")
        await show_group_stats(callback, state, role)
        await state.set_state(states_group.group_stats_result)

    # Обработчики для отправки сообщений студентам
    @router.callback_query(states_group.student_stats_list, F.data.startswith("hw_message_student_"))
    async def role_enter_homework_message(callback: CallbackQuery, state: FSMContext):
        logging.info(f"ВЫЗОВ: role_enter_homework_message | КОЛБЭК: {callback.data} | СОСТОЯНИЕ: {await state.get_state()}")
        await enter_homework_message(callback, state, role)
        await state.set_state(states_group.enter_homework_message)

    @router.message(states_group.enter_homework_message)
    async def role_confirm_homework_message(message, state: FSMContext):
        logging.info(f"ВЫЗОВ: role_confirm_homework_message | СООБЩЕНИЕ: {message.text[:50]}... | СОСТОЯНИЕ: {await state.get_state()}")
        await confirm_homework_message(message, state, role)
        await state.set_state(states_group.confirm_homework_message)

    @router.callback_query(states_group.confirm_homework_message, F.data == "send_homework_message")
    async def role_send_homework_message(callback: CallbackQuery, state: FSMContext):
        logging.info(f"ВЫЗОВ: role_send_homework_message | КОЛБЭК: {callback.data} | СОСТОЯНИЕ: {await state.get_state()}")
        await send_homework_message(callback, state, role)

    @router.callback_query(states_group.confirm_homework_message, F.data == "cancel_homework_message")
    async def role_cancel_homework_message(callback: CallbackQuery, state: FSMContext):
        logging.info(f"ВЫЗОВ: role_cancel_homework_message | КОЛБЭК: {callback.data} | СОСТОЯНИЕ: {await state.get_state()}")
        await cancel_homework_message(callback, state, role)
