import logging
from aiogram import F, Router
from aiogram.types import CallbackQuery
from aiogram.fsm.context import FSMContext
from .handlers import (
    show_homework_menu, select_student_stats_course, select_student_stats_group,
    select_student_stats_lesson, show_student_stats_list, select_group_stats_group,
    show_group_stats, enter_homework_message, confirm_homework_message,
    send_homework_message, cancel_homework_message, back_from_homework_group_microtopics_to_stats
)

# Настройка логирования
logging.basicConfig(level=logging.INFO)

def register_homework_handlers(router: Router, states_group, role: str):
    """Регистрация обработчиков для работы с домашними заданиями"""
    
    @router.callback_query(F.data == f"{role}_homeworks")
    async def show_role_homework_menu(callback: CallbackQuery, state: FSMContext):
        logging.info(f"ВЫЗОВ: show_role_homework_menu | КОЛБЭК: {callback.data} | СОСТОЯНИЕ: {await state.get_state()}")
        await show_homework_menu(callback, state, role)
        await state.set_state(states_group.homework_menu)

    @router.callback_query(states_group.homework_menu, F.data == "hw_student_stats")
    async def role_select_student_stats_course(callback: CallbackQuery, state: FSMContext):
        logging.info(f"ВЫЗОВ: role_select_student_stats_course | КОЛБЭК: {callback.data} | СОСТОЯНИЕ: {await state.get_state()}")
        await select_student_stats_course(callback, state, role)
        await state.set_state(states_group.student_stats_course)

    @router.callback_query(states_group.student_stats_course, F.data.startswith("course_"))
    async def role_select_student_stats_group(callback: CallbackQuery, state: FSMContext):
        logging.info(f"ВЫЗОВ: role_select_student_stats_group | КОЛБЭК: {callback.data} | СОСТОЯНИЕ: {await state.get_state()}")
        await select_student_stats_group(callback, state, role)
        await state.set_state(states_group.student_stats_group)

    @router.callback_query(states_group.student_stats_group, F.data.startswith("analytics_group_"))
    async def role_select_student_stats_lesson(callback: CallbackQuery, state: FSMContext):
        logging.info(f"ВЫЗОВ: role_select_student_stats_lesson | КОЛБЭК: {callback.data} | СОСТОЯНИЕ: {await state.get_state()}")
        await select_student_stats_lesson(callback, state, role)
        await state.set_state(states_group.student_stats_lesson)

    @router.callback_query(states_group.student_stats_lesson, F.data.startswith("lesson_"))
    async def role_show_student_stats_list(callback: CallbackQuery, state: FSMContext):
        logging.info(f"ВЫЗОВ: role_show_student_stats_list | КОЛБЭК: {callback.data} | СОСТОЯНИЕ: {await state.get_state()}")
        await show_student_stats_list(callback, state, role)
        await state.set_state(states_group.student_stats_list)

    @router.callback_query(states_group.homework_menu, F.data == "hw_group_stats")
    async def role_select_group_stats_group(callback: CallbackQuery, state: FSMContext):
        logging.info(f"ВЫЗОВ: role_select_group_stats_group | КОЛБЭК: {callback.data} | СОСТОЯНИЕ: {await state.get_state()}")
        await select_group_stats_group(callback, state, role)
        await state.set_state(states_group.group_stats_group)

    @router.callback_query(states_group.group_stats_group, F.data.startswith("analytics_group_"))
    async def role_show_group_stats(callback: CallbackQuery, state: FSMContext):
        logging.info(f"ВЫЗОВ: role_show_group_stats | КОЛБЭК: {callback.data} | СОСТОЯНИЕ: {await state.get_state()}")
        await show_group_stats(callback, state, role)
        await state.set_state(states_group.group_stats_result)

    # Обработчики для отправки сообщений студентам
    @router.callback_query(states_group.student_stats_list, F.data.startswith("hw_message_student_"))
    async def role_enter_homework_message(callback: CallbackQuery, state: FSMContext):
        logging.info(f"ВЫЗОВ: role_enter_homework_message | КОЛБЭК: {callback.data} | СОСТОЯНИЕ: {await state.get_state()}")
        await enter_homework_message(callback, state, role)
        await state.set_state(states_group.enter_homework_message)

    @router.message(states_group.enter_homework_message)
    async def role_confirm_homework_message(message, state: FSMContext):
        logging.info(f"ВЫЗОВ: role_confirm_homework_message | СООБЩЕНИЕ: {message.text[:50]}... | СОСТОЯНИЕ: {await state.get_state()}")
        await confirm_homework_message(message, state, role)
        await state.set_state(states_group.confirm_homework_message)

    @router.callback_query(states_group.confirm_homework_message, F.data == "send_homework_message")
    async def role_send_homework_message(callback: CallbackQuery, state: FSMContext):
        logging.info(f"ВЫЗОВ: role_send_homework_message | КОЛБЭК: {callback.data} | СОСТОЯНИЕ: {await state.get_state()}")
        await send_homework_message(callback, state, role)

    @router.callback_query(states_group.confirm_homework_message, F.data == "cancel_homework_message")
    async def role_cancel_homework_message(callback: CallbackQuery, state: FSMContext):
        logging.info(f"ВЫЗОВ: role_cancel_homework_message | КОЛБЭК: {callback.data} | СОСТОЯНИЕ: {await state.get_state()}")
        await cancel_homework_message(callback, state, role)
