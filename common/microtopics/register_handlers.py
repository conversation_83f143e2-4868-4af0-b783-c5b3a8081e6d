import logging
from aiogram import F, Router
from aiogram.types import CallbackQuery
from aiogram.fsm.context import FSMContext
from .handlers import (
    handle_microtopics_pagination_universal,
    show_detailed_microtopics_from_callback,
    show_summary_microtopics_from_callback,
    show_subject_detailed_microtopics_from_callback,
    show_subject_summary_microtopics_from_callback,
    show_month_entry_test_detailed_from_callback,
    show_month_entry_test_summary_from_callback,
    back_from_month_entry_test_microtopics_to_result,
    show_month_control_test_detailed_from_callback,
    show_month_control_test_summary_from_callback,
    back_from_month_control_test_microtopics_to_result
)


# Настройка логирования
logging.basicConfig(level=logging.INFO)


def register_microtopics_handlers(
    router: Router,
    states_group,
    role: str,
    detailed_callback_prefix: str,
    summary_callback_prefix: str,
    detailed_state,
    summary_state,
    subject_details_state,
    back_keyboard_func,
    back_from_image_func,            # Функция возврата из изображений микротем
    title: str,                      # Заголовок для микротем
    items_per_page_detailed: int = 15,
    items_per_page_summary: int = 15,
    premium_check: bool = True
):
    """
    Регистрация универсальных обработчиков микротем для роли

    Args:
        router: Роутер для регистрации обработчиков
        states_group: Группа состояний роли
        role: Роль пользователя ("student", "curator", "teacher", etc.)
        detailed_callback_prefix: Префикс для детальной пагинации (например: "student_microtopics_page")
        summary_callback_prefix: Префикс для пагинации сводки (например: "student_summary_page")
        detailed_state: Состояние детальной статистики
        summary_state: Состояние сводки
        subject_details_state: Состояние выбора предмета
        back_keyboard_func: Функция для кнопки "Назад"
        back_from_image_func: Функция возврата из изображений микротем
        title: Заголовок для микротем
        items_per_page_detailed: Элементов на страницу для детальной статистики
        items_per_page_summary: Элементов на страницу для сводки
        premium_check: Нужна ли проверка премиума
    """

    # Основные обработчики отображения микротем
    @router.callback_query(subject_details_state, F.data.startswith("microtopics_detailed_"))
    async def show_detailed_microtopics(callback: CallbackQuery, state: FSMContext):
        logging.info(f"ВЫЗОВ: show_detailed_microtopics | РОЛЬ: {role} | КОЛБЭК: {callback.data} | СОСТОЯНИЕ: {await state.get_state()}")
        await show_detailed_microtopics_from_callback(
            callback=callback,
            state=state,
            role=role,
            target_state=detailed_state,
            callback_prefix=detailed_callback_prefix,
            back_keyboard_func=back_keyboard_func,
            title=title,
            items_per_page=items_per_page_detailed,
            caption="📊 Детальная статистика по микротемам",
            premium_check=premium_check,
            premium_feature="detailed_analytics"
        )

    @router.callback_query(subject_details_state, F.data.startswith("microtopics_summary_"))
    async def show_summary_microtopics(callback: CallbackQuery, state: FSMContext):
        logging.info(f"ВЫЗОВ: show_summary_microtopics | РОЛЬ: {role} | КОЛБЭК: {callback.data} | СОСТОЯНИЕ: {await state.get_state()}")
        await show_summary_microtopics_from_callback(
            callback=callback,
            state=state,
            role=role,
            target_state=summary_state,
            callback_prefix=summary_callback_prefix,
            back_keyboard_func=back_keyboard_func,
            title=title,
            items_per_page=items_per_page_summary,
            caption="📊 Сводка по сильным и слабым темам",
            premium_check=premium_check,
            premium_feature="advanced_statistics"
        )

    # Обработчики пагинации для детальной статистики микротем
    @router.callback_query(detailed_state, F.data.startswith(f"{detailed_callback_prefix}_"))
    async def handle_detailed_microtopics_pagination(callback: CallbackQuery, state: FSMContext):
        logging.info(f"ВЫЗОВ: handle_detailed_microtopics_pagination | РОЛЬ: {role} | КОЛБЭК: {callback.data} | СОСТОЯНИЕ: {await state.get_state()}")
        await handle_microtopics_pagination_universal(
            callback=callback,
            state=state,
            callback_prefix=detailed_callback_prefix,
            display_mode="detailed",
            role=role
        )

    # Обработчики пагинации для сводки микротем
    @router.callback_query(summary_state, F.data.startswith(f"{summary_callback_prefix}_"))
    async def handle_summary_microtopics_pagination(callback: CallbackQuery, state: FSMContext):
        logging.info(f"ВЫЗОВ: handle_summary_microtopics_pagination | РОЛЬ: {role} | КОЛБЭК: {callback.data} | СОСТОЯНИЕ: {await state.get_state()}")
        await handle_microtopics_pagination_universal(
            callback=callback,
            state=state,
            callback_prefix=summary_callback_prefix,
            display_mode="summary",
            role=role
        )

    # Универсальный обработчик возврата из изображений микротем (с фильтрацией по состояниям)
    @router.callback_query(detailed_state, F.data == "back_from_microtopics_image")
    async def back_from_detailed_microtopics_image(callback: CallbackQuery, state: FSMContext):
        """Возврат из детальной статистики микротем"""
        logging.info(f"ВЫЗОВ: back_from_detailed_microtopics_image | РОЛЬ: {role} | КОЛБЭК: {callback.data} | СОСТОЯНИЕ: {await state.get_state()}")
        await back_from_image_func(callback, state)

    @router.callback_query(summary_state, F.data == "back_from_microtopics_image")
    async def back_from_summary_microtopics_image(callback: CallbackQuery, state: FSMContext):
        """Возврат из сводки микротем"""
        logging.info(f"ВЫЗОВ: back_from_summary_microtopics_image | РОЛЬ: {role} | КОЛБЭК: {callback.data} | СОСТОЯНИЕ: {await state.get_state()}")
        await back_from_image_func(callback, state)


def register_subject_microtopics_handlers(
    router: Router,
    states_group,
    role: str,
    detailed_callback_prefix: str,
    summary_callback_prefix: str,
    detailed_state,
    summary_state,
    subject_stats_state,
    back_keyboard_func,
    back_from_image_func,            # Функция возврата из изображений микротем предмета
    items_per_page_detailed: int = 15,
    items_per_page_summary: int = 15,
    premium_check: bool = False
):
    """
    Регистрация универсальных обработчиков микротем предмета

    Args:
        router: Роутер для регистрации обработчиков
        states_group: Группа состояний роли
        detailed_callback_prefix: Префикс для детальной пагинации (например: "manager_subject_microtopics_page")
        summary_callback_prefix: Префикс для пагинации сводки (например: "manager_subject_summary_page")
        detailed_state: Состояние детальной статистики
        summary_state: Состояние сводки
        subject_stats_state: Состояние статистики предмета
        back_keyboard_func: Функция для кнопки "Назад"
        back_from_image_func: Функция возврата из изображений микротем предмета
        items_per_page_detailed: Элементов на страницу для детальной статистики
        items_per_page_summary: Элементов на страницу для сводки
        premium_check: Нужна ли проверка премиума
    """

    # Основные обработчики отображения микротем предмета
    @router.callback_query(subject_stats_state, F.data.startswith("subject_microtopics_detailed_"))
    async def show_subject_detailed_microtopics(callback: CallbackQuery, state: FSMContext):
        logging.info(f"ВЫЗОВ: show_subject_detailed_microtopics | РОЛЬ: {role} | КОЛБЭК: {callback.data} | СОСТОЯНИЕ: {await state.get_state()}")
        await show_subject_detailed_microtopics_from_callback(
            callback=callback,
            state=state,
            role=role,
            target_state=detailed_state,
            callback_prefix=detailed_callback_prefix,
            back_keyboard_func=back_keyboard_func,
            items_per_page=items_per_page_detailed,
            caption="📊 Детальная статистика по микротемам предмета",
            premium_check=premium_check
        )

    @router.callback_query(subject_stats_state, F.data.startswith("subject_microtopics_summary_"))
    async def show_subject_summary_microtopics(callback: CallbackQuery, state: FSMContext):
        logging.info(f"ВЫЗОВ: show_subject_summary_microtopics | РОЛЬ: {role} | КОЛБЭК: {callback.data} | СОСТОЯНИЕ: {await state.get_state()}")
        await show_subject_summary_microtopics_from_callback(
            callback=callback,
            state=state,
            role=role,
            target_state=summary_state,
            callback_prefix=summary_callback_prefix,
            back_keyboard_func=back_keyboard_func,
            items_per_page=items_per_page_summary,
            caption="📊 Сводка по сильным и слабым темам предмета",
            premium_check=premium_check
        )

    # Обработчики пагинации для детальной статистики микротем предмета
    @router.callback_query(detailed_state, F.data.startswith(f"{detailed_callback_prefix}_"))
    async def handle_subject_detailed_microtopics_pagination(callback: CallbackQuery, state: FSMContext):
        logging.info(f"ВЫЗОВ: handle_subject_detailed_microtopics_pagination | РОЛЬ: {role} | КОЛБЭК: {callback.data} | СОСТОЯНИЕ: {await state.get_state()}")
        await handle_microtopics_pagination_universal(
            callback=callback,
            state=state,
            callback_prefix=detailed_callback_prefix,
            display_mode="detailed",
            role=role
        )

    # Обработчики пагинации для сводки микротем предмета
    @router.callback_query(summary_state, F.data.startswith(f"{summary_callback_prefix}_"))
    async def handle_subject_summary_microtopics_pagination(callback: CallbackQuery, state: FSMContext):
        logging.info(f"ВЫЗОВ: handle_subject_summary_microtopics_pagination | РОЛЬ: {role} | КОЛБЭК: {callback.data} | СОСТОЯНИЕ: {await state.get_state()}")
        await handle_microtopics_pagination_universal(
            callback=callback,
            state=state,
            callback_prefix=summary_callback_prefix,
            display_mode="summary",
            role=role
        )

    # Универсальный обработчик возврата из изображений микротем предмета (с фильтрацией по состояниям)
    @router.callback_query(detailed_state, F.data == "back_from_microtopics_image")
    async def back_from_subject_detailed_microtopics_image(callback: CallbackQuery, state: FSMContext):
        """Возврат из детальной статистики микротем предмета"""
        logging.info(f"ВЫЗОВ: back_from_subject_detailed_microtopics_image | РОЛЬ: {role} | КОЛБЭК: {callback.data} | СОСТОЯНИЕ: {await state.get_state()}")
        await back_from_image_func(callback, state, role)

    @router.callback_query(summary_state, F.data == "back_from_microtopics_image")
    async def back_from_subject_summary_microtopics_image(callback: CallbackQuery, state: FSMContext):
        """Возврат из сводки микротем предмета"""
        logging.info(f"ВЫЗОВ: back_from_subject_summary_microtopics_image | РОЛЬ: {role} | КОЛБЭК: {callback.data} | СОСТОЯНИЕ: {await state.get_state()}")
        await back_from_image_func(callback, state, role)


def register_month_entry_test_microtopics_handlers(
    router: Router,
    states_group,
    detailed_callback_prefix: str,
    summary_callback_prefix: str,
    detailed_state,
    summary_state,
    result_state,
    back_keyboard_func,
    items_per_page_detailed: int = 15,
    items_per_page_summary: int = 15,
    premium_check: bool = False
):
    """
    Регистрация универсальных обработчиков микротем для входных тестов месяца

    Args:
        router: Роутер для регистрации обработчиков
        states_group: Группа состояний
        detailed_callback_prefix: Префикс для детальной пагинации (например: "student_month_entry_page")
        summary_callback_prefix: Префикс для пагинации сводки (например: "student_month_entry_summary_page")
        detailed_state: Состояние детальной статистики
        summary_state: Состояние сводки
        result_state: Состояние результата теста
        back_keyboard_func: Функция для кнопки "Назад"
        items_per_page_detailed: Элементов на страницу для детальной статистики
        items_per_page_summary: Элементов на страницу для сводки
        premium_check: Нужна ли проверка премиума
    """

    # Основные обработчики отображения микротем входного теста месяца
    @router.callback_query(result_state, F.data.startswith("student_month_entry_detailed_"))
    async def show_month_entry_detailed_microtopics(callback: CallbackQuery, state: FSMContext):
        logging.info(f"ВЫЗОВ: show_month_entry_detailed_microtopics | КОЛБЭК: {callback.data} | СОСТОЯНИЕ: {await state.get_state()}")
        await show_month_entry_test_detailed_from_callback(
            callback=callback,
            state=state,
            target_state=detailed_state,
            callback_prefix=detailed_callback_prefix,
            back_keyboard_func=back_keyboard_func,
            items_per_page=items_per_page_detailed,
            caption="📊 Детальная статистика входного теста месяца",
            premium_check=premium_check
        )

    @router.callback_query(result_state, F.data.startswith("student_month_entry_summary_"))
    async def show_month_entry_summary_microtopics(callback: CallbackQuery, state: FSMContext):
        logging.info(f"ВЫЗОВ: show_month_entry_summary_microtopics | КОЛБЭК: {callback.data} | СОСТОЯНИЕ: {await state.get_state()}")
        await show_month_entry_test_summary_from_callback(
            callback=callback,
            state=state,
            target_state=summary_state,
            callback_prefix=summary_callback_prefix,
            back_keyboard_func=back_keyboard_func,
            items_per_page=items_per_page_summary,
            caption="📊 Сводка по сильным и слабым темам входного теста",
            premium_check=premium_check
        )

    # Обработчики пагинации для детальной статистики микротем входного теста
    @router.callback_query(detailed_state, F.data.startswith(f"{detailed_callback_prefix}_"))
    async def handle_month_entry_detailed_microtopics_pagination(callback: CallbackQuery, state: FSMContext):
        logging.info(f"ВЫЗОВ: handle_month_entry_detailed_microtopics_pagination | КОЛБЭК: {callback.data} | СОСТОЯНИЕ: {await state.get_state()}")
        await handle_microtopics_pagination_universal(
            callback=callback,
            state=state,
            callback_prefix=detailed_callback_prefix,
            display_mode="detailed",
            role="student"
        )

    # Обработчики пагинации для сводки микротем входного теста
    @router.callback_query(summary_state, F.data.startswith(f"{summary_callback_prefix}_"))
    async def handle_month_entry_summary_microtopics_pagination(callback: CallbackQuery, state: FSMContext):
        logging.info(f"ВЫЗОВ: handle_month_entry_summary_microtopics_pagination | КОЛБЭК: {callback.data} | СОСТОЯНИЕ: {await state.get_state()}")
        await handle_microtopics_pagination_universal(
            callback=callback,
            state=state,
            callback_prefix=summary_callback_prefix,
            display_mode="summary",
            role="student"
        )

    # Универсальный обработчик возврата из изображений микротем входного теста (с фильтрацией по состояниям)
    @router.callback_query(detailed_state, F.data == "back_from_microtopics_image")
    async def back_from_month_entry_detailed_microtopics_image(callback: CallbackQuery, state: FSMContext):
        """Возврат из детальной статистики микротем входного теста"""
        logging.info(f"ВЫЗОВ: back_from_month_entry_detailed_microtopics_image | КОЛБЭК: {callback.data} | СОСТОЯНИЕ: {await state.get_state()}")
        await back_from_month_entry_test_microtopics_to_result(callback, state)

    @router.callback_query(summary_state, F.data == "back_from_microtopics_image")
    async def back_from_month_entry_summary_microtopics_image(callback: CallbackQuery, state: FSMContext):
        """Возврат из сводки микротем входного теста"""
        logging.info(f"ВЫЗОВ: back_from_month_entry_summary_microtopics_image | КОЛБЭК: {callback.data} | СОСТОЯНИЕ: {await state.get_state()}")
        await back_from_month_entry_test_microtopics_to_result(callback, state)


def register_curator_month_entry_test_microtopics_handlers(
    router: Router,
    states_group,
    detailed_callback_prefix: str,
    summary_callback_prefix: str,
    detailed_state,
    summary_state,
    result_state,
    back_keyboard_func,
    items_per_page_detailed: int = 15,
    items_per_page_summary: int = 15,
    premium_check: bool = False
):
    """
    Регистрация универсальных обработчиков микротем для входных тестов месяца (для куратора)

    Args:
        router: Роутер для регистрации обработчиков
        states_group: Группа состояний
        detailed_callback_prefix: Префикс для детальной пагинации (например: "curator_month_entry_page")
        summary_callback_prefix: Префикс для пагинации сводки (например: "curator_month_entry_summary_page")
        detailed_state: Состояние детальной статистики
        summary_state: Состояние сводки
        result_state: Состояние результата теста
        back_keyboard_func: Функция для кнопки "Назад"
        items_per_page_detailed: Элементов на страницу для детальной статистики
        items_per_page_summary: Элементов на страницу для сводки
        premium_check: Нужна ли проверка премиума
    """



    # Обработчики пагинации для детальной статистики микротем
    @router.callback_query(detailed_state, F.data.startswith(f"{detailed_callback_prefix}_"))
    async def curator_month_entry_detailed_pagination_handler(callback: CallbackQuery, state: FSMContext):
        logging.info(f"ВЫЗОВ: curator_month_entry_detailed_pagination | КОЛБЭК: {callback.data} | СОСТОЯНИЕ: {await state.get_state()}")
        await handle_microtopics_pagination_universal(callback, state)

    # Обработчики пагинации для сводки микротем
    @router.callback_query(summary_state, F.data.startswith(f"{summary_callback_prefix}_"))
    async def curator_month_entry_summary_pagination_handler(callback: CallbackQuery, state: FSMContext):
        logging.info(f"ВЫЗОВ: curator_month_entry_summary_pagination | КОЛБЭК: {callback.data} | СОСТОЯНИЕ: {await state.get_state()}")
        await handle_microtopics_pagination_universal(callback, state)

    # Универсальный обработчик возврата из изображений микротем входного теста (с фильтрацией по состояниям)

    @router.callback_query(detailed_state, F.data == "back_from_microtopics_image")
    async def back_from_curator_month_entry_detailed_microtopics_image(callback: CallbackQuery, state: FSMContext):
        """Возврат из детальной статистики микротем входного теста куратора"""
        current_state = await state.get_state()

        from common.microtopics.curator_adapter import curator_back_from_month_entry_microtopics_to_result
        await curator_back_from_month_entry_microtopics_to_result(callback, state)

    logging.error(f"🔧 РЕГИСТРИРУЕМ ОБРАБОТЧИК ВОЗВРАТА SUMMARY для состояния: {summary_state}")

    @router.callback_query(summary_state, F.data == "back_from_microtopics_image")
    async def back_from_curator_month_entry_summary_microtopics_image(callback: CallbackQuery, state: FSMContext):
        """Возврат из сводки микротем входного теста куратора"""
        current_state = await state.get_state()

        from common.microtopics.curator_adapter import curator_back_from_month_entry_microtopics_to_result
        await curator_back_from_month_entry_microtopics_to_result(callback, state)

    # СТАРЫЙ обработчик - ОТКЛЮЧЕН (заменен на универсальный)
    # @router.callback_query(detailed_state, F.data == "curator_back_to_month_entry_result")
    async def curator_back_to_month_entry_result_detailed_OLD(callback: CallbackQuery, state: FSMContext):
        """Возврат к результату теста из детальной статистики микротем"""
        current_state = await state.get_state()


        # Получаем сохраненные данные
        data = await state.get_data()
        group_id = data.get('month_entry_group_id')
        month_test_id = data.get('month_entry_month_test_id')
        student_id = data.get('month_entry_student_id')

        logging.error(f"🔙 ДАННЫЕ ИЗ СОСТОЯНИЯ: group_id={group_id}, month_test_id={month_test_id}, student_id={student_id}")

        if all([group_id, month_test_id, student_id]):
            logging.error(f"🔙 ВЫЗЫВАЕМ show_month_entry_student_detail с параметрами: {group_id}, {month_test_id}, {student_id}")
            # Вызываем напрямую функцию отображения результата теста
            from common.tests_statistics.handlers import show_month_entry_student_detail
            await show_month_entry_student_detail(callback, state, group_id, month_test_id, student_id)

            # Устанавливаем правильное состояние
            from curator.states.states_tests import CuratorTestsStatisticsStates
            await state.set_state(CuratorTestsStatisticsStates.month_entry_result)
            logging.error(f"🔙 УСТАНОВЛЕНО СОСТОЯНИЕ: {CuratorTestsStatisticsStates.month_entry_result}")
        else:
            logging.error(f"🔙 FALLBACK: Недостаточно данных, используем адаптер")
            # Fallback через адаптер
            from common.microtopics.curator_adapter import curator_back_from_month_entry_microtopics_to_result
            await curator_back_from_month_entry_microtopics_to_result(callback, state)

    # СТАРЫЙ обработчик - ОТКЛЮЧЕН (заменен на универсальный)
    # @router.callback_query(summary_state, F.data == "curator_back_to_month_entry_result")
    async def curator_back_to_month_entry_result_summary_OLD(callback: CallbackQuery, state: FSMContext):
        """Возврат к результату теста из сводки микротем"""
        current_state = await state.get_state()
        logging.error(f"🔙🔙🔙 СПЕЦИФИЧНЫЙ ОБРАБОТЧИК КУРАТОРА SUMMARY | USER: {callback.from_user.id} | CALLBACK: '{callback.data}' | STATE: {current_state} | FUNCTION: curator_back_to_month_entry_result_summary")

        # Получаем сохраненные данные
        data = await state.get_data()
        group_id = data.get('month_entry_group_id')
        month_test_id = data.get('month_entry_month_test_id')
        student_id = data.get('month_entry_student_id')

        logging.error(f"🔙 ДАННЫЕ ИЗ СОСТОЯНИЯ: group_id={group_id}, month_test_id={month_test_id}, student_id={student_id}")

        if all([group_id, month_test_id, student_id]):
            logging.error(f"🔙 ВЫЗЫВАЕМ show_month_entry_student_detail с параметрами: {group_id}, {month_test_id}, {student_id}")
            # Вызываем напрямую функцию отображения результата теста
            from common.tests_statistics.handlers import show_month_entry_student_detail
            await show_month_entry_student_detail(callback, state, group_id, month_test_id, student_id)

            # Устанавливаем правильное состояние
            from curator.states.states_tests import CuratorTestsStatisticsStates
            await state.set_state(CuratorTestsStatisticsStates.month_entry_result)
            logging.error(f"🔙 УСТАНОВЛЕНО СОСТОЯНИЕ: {CuratorTestsStatisticsStates.month_entry_result}")
        else:
            logging.error(f"🔙 FALLBACK: Недостаточно данных, используем адаптер")
            # Fallback через адаптер
            from common.microtopics.curator_adapter import curator_back_from_month_entry_microtopics_to_result
            await curator_back_from_month_entry_microtopics_to_result(callback, state)

    # Логируем состояния для отладки
    logging.error(f"🔍 ОТЛАДКА СОСТОЯНИЙ:")
    logging.error(f"🔍 detailed_state: {detailed_state}")
    logging.error(f"🔍 summary_state: {summary_state}")
    logging.error(f"🔍 detailed_state type: {type(detailed_state)}")
    logging.error(f"🔍 summary_state type: {type(summary_state)}")

    # Проверяем состояния куратора напрямую
    from curator.states.states_tests import CuratorTestsStatisticsStates
    logging.error(f"🔍 CuratorTestsStatisticsStates.month_entry_detailed_microtopics: {CuratorTestsStatisticsStates.month_entry_detailed_microtopics}")
    logging.error(f"🔍 CuratorTestsStatisticsStates.month_entry_summary_microtopics: {CuratorTestsStatisticsStates.month_entry_summary_microtopics}")

    # Сравниваем
    logging.error(f"🔍 detailed_state == CuratorTestsStatisticsStates.month_entry_detailed_microtopics: {detailed_state == CuratorTestsStatisticsStates.month_entry_detailed_microtopics}")
    logging.error(f"🔍 summary_state == CuratorTestsStatisticsStates.month_entry_summary_microtopics: {summary_state == CuratorTestsStatisticsStates.month_entry_summary_microtopics}")

    # Универсальный обработчик для кураторов/учителей/менеджеров БЕЗ фильтрации состояний, но с проверкой внутри
    @router.callback_query(F.data == "back_to_month_entry_result")
    async def universal_back_to_month_entry_result(callback: CallbackQuery, state: FSMContext):
        """Универсальный обработчик для кураторов/учителей/менеджеров с проверкой состояния внутри"""
        current_state = await state.get_state()

        # Определяем роль по состоянию (ТОЛЬКО для кураторов/учителей/менеджеров)
        role = None
        target_state_class = None

        if str(current_state).startswith('CuratorTestsStatisticsStates:'):
            role = "curator"
            from curator.states.states_tests import CuratorTestsStatisticsStates
            target_state_class = CuratorTestsStatisticsStates
        elif str(current_state).startswith('TeacherTestsStatisticsStates:'):
            role = "teacher"
            from teacher.states.states_tests import TeacherTestsStatisticsStates
            target_state_class = TeacherTestsStatisticsStates
        elif str(current_state).startswith('ManagerTestsStatisticsStates:'):
            role = "manager"
            from manager.states.states_tests import ManagerTestsStatisticsStates
            target_state_class = ManagerTestsStatisticsStates
        elif str(current_state).startswith('StudentTestsStates:'):
            # Для студентов используем их собственный обработчик возврата
            await back_from_month_entry_test_microtopics_to_result(callback, state)
            return
        else:
            # Если не одна из поддерживаемых ролей - игнорируем
            return

        logging.info(f"🔍 УНИВЕРСАЛЬНЫЙ ОБРАБОТЧИК СРАБОТАЛ! ROLE: {role} | USER: {callback.from_user.id} | CURRENT_STATE: {current_state}")

        # Вызываем основную логику для кураторов/учителей/менеджеров
        data = await state.get_data()
        group_id = data.get('month_entry_group_id')
        month_test_id = data.get('month_entry_month_test_id')
        student_id = data.get('month_entry_student_id')

        if all([group_id, month_test_id, student_id]):
            try:
                # Удаляем сообщение с изображением
                await callback.message.delete()
            except Exception as e:
                pass  # Игнорируем ошибки удаления

            # Используем нашу собственную логику вместо show_month_entry_student_detail
            try:
                from database import MonthEntryTestResultRepository, StudentRepository, GroupRepository, MonthTestRepository, MicrotopicRepository
                from aiogram.types import InlineKeyboardButton, InlineKeyboardMarkup

                # Получаем данные (копия логики из show_month_entry_student_detail)
                test_result = await MonthEntryTestResultRepository.get_by_student_and_month_test(student_id, month_test_id)
                if not test_result:
                    await callback.message.answer("❌ Результат теста не найден")
                    return

                student = await StudentRepository.get_by_id(student_id)
                group = await GroupRepository.get_by_id(group_id)
                month_test = await MonthTestRepository.get_by_id(month_test_id)

                if not all([student, group, month_test]):
                    await callback.message.answer("❌ Данные не найдены")
                    return

                # Формируем текст результата
                result_text = f"📊 Результат входного теста месяца\n\n"
                result_text += f"📗 {group.subject.name}:\n"
                result_text += f"Тест: {month_test.name}\n"
                result_text += f"Верных: {test_result.correct_answers} / {test_result.total_questions}\n"
                result_text += f"Процент: {test_result.score_percentage}%\n"

                # Добавляем рост (если есть атрибут)
                if hasattr(test_result, 'growth_percentage') and test_result.growth_percentage is not None:
                    if test_result.growth_percentage > 0:
                        result_text += f"📈 Рост по тесту: +{test_result.growth_percentage}%\n"
                    elif test_result.growth_percentage < 0:
                        result_text += f"📉 Рост по тесту: {test_result.growth_percentage}%\n"
                    else:
                        result_text += f"➡️ Рост по тесту: {test_result.growth_percentage}%\n"

                result_text += "\nВыберите тип аналитики:"

                # Создаем кнопки для кураторов/учителей/менеджеров
                buttons = [
                    [InlineKeyboardButton(
                        text="📊 Проценты по микротемам",
                        callback_data=f"month_entry_detailed_{group_id}_{month_test_id}_{student_id}"
                    )],
                    [InlineKeyboardButton(
                        text="💪 Сильные/слабые темы",
                        callback_data=f"month_entry_summary_{group_id}_{month_test_id}_{student_id}"
                    )]
                ]

                # Для кураторов/учителей/менеджеров используем стандартную кнопку "Назад"
                from common.tests_statistics.keyboards import get_main_menu_back_button
                buttons.extend(get_main_menu_back_button())

                # Сохраняем данные для навигации
                await state.update_data(
                    month_entry_group_id=group_id,
                    month_entry_month_test_id=month_test_id,
                    month_entry_student_id=student_id
                )

                # Отправляем новое сообщение
                await callback.message.answer(
                    result_text,
                    reply_markup=InlineKeyboardMarkup(inline_keyboard=buttons)
                )

                # Устанавливаем правильное состояние для соответствующей роли
                await state.set_state(target_state_class.month_entry_result)

            except Exception as e:
                logging.error(f"❌ Ошибка в универсальном обработчике: {e}")
                await callback.message.answer("❌ Ошибка при получении данных")
        else:
            await callback.answer("❌ Ошибка: данные не найдены")

def register_month_control_test_microtopics_handlers(
    router: Router,
    states_group,
    detailed_callback_prefix: str,
    summary_callback_prefix: str,
    detailed_state,
    summary_state,
    result_state,
    back_keyboard_func,
    items_per_page_detailed: int = 15,
    items_per_page_summary: int = 15,
    premium_check: bool = False
):
    """
    Регистрация универсальных обработчиков микротем для контрольных тестов месяца

    Args:
        router: Роутер для регистрации обработчиков
        states_group: Группа состояний
        detailed_callback_prefix: Префикс для детальной пагинации (например: "student_month_control_page")
        summary_callback_prefix: Префикс для пагинации сводки (например: "student_month_control_summary_page")
        detailed_state: Состояние детальной статистики
        summary_state: Состояние сводки
        result_state: Состояние результата теста
        back_keyboard_func: Функция для кнопки "Назад"
        items_per_page_detailed: Элементов на страницу для детальной статистики
        items_per_page_summary: Элементов на страницу для сводки
        premium_check: Нужна ли проверка премиума
    """

    # Основные обработчики отображения микротем контрольного теста месяца
    @router.callback_query(result_state, F.data.startswith("month_control_detailed_"))
    async def show_month_control_detailed_microtopics(callback: CallbackQuery, state: FSMContext):
        logging.info(f"ВЫЗОВ: show_month_control_detailed_microtopics | КОЛБЭК: {callback.data} | СОСТОЯНИЕ: {await state.get_state()}")
        await show_month_control_test_detailed_from_callback(
            callback=callback,
            state=state,
            target_state=detailed_state,
            callback_prefix=detailed_callback_prefix,
            back_keyboard_func=back_keyboard_func,
            items_per_page=items_per_page_detailed,
            caption="📊 Детальная статистика контрольного теста месяца",
            premium_check=premium_check
        )

    @router.callback_query(result_state, F.data.startswith("month_control_summary_"))
    async def show_month_control_summary_microtopics(callback: CallbackQuery, state: FSMContext):
        logging.info(f"ВЫЗОВ: show_month_control_summary_microtopics | КОЛБЭК: {callback.data} | СОСТОЯНИЕ: {await state.get_state()}")
        await show_month_control_test_summary_from_callback(
            callback=callback,
            state=state,
            target_state=summary_state,
            callback_prefix=summary_callback_prefix,
            back_keyboard_func=back_keyboard_func,
            items_per_page=items_per_page_summary,
            caption="📊 Сводка по сильным и слабым темам контрольного теста",
            premium_check=premium_check
        )

    # Обработчики для студентов (с префиксом student_)
    @router.callback_query(result_state, F.data.startswith("student_month_control_detailed_"))
    async def show_student_month_control_detailed_microtopics(callback: CallbackQuery, state: FSMContext):
        logging.info(f"ВЫЗОВ: show_student_month_control_detailed_microtopics | КОЛБЭК: {callback.data} | СОСТОЯНИЕ: {await state.get_state()}")
        await show_month_control_test_detailed_from_callback(
            callback=callback,
            state=state,
            target_state=detailed_state,
            callback_prefix=detailed_callback_prefix,
            back_keyboard_func=back_keyboard_func,
            items_per_page=items_per_page_detailed,
            caption="📊 Детальная статистика контрольного теста месяца",
            premium_check=premium_check
        )

    @router.callback_query(result_state, F.data.startswith("student_month_control_summary_"))
    async def show_student_month_control_summary_microtopics(callback: CallbackQuery, state: FSMContext):
        logging.info(f"ВЫЗОВ: show_student_month_control_summary_microtopics | КОЛБЭК: {callback.data} | СОСТОЯНИЕ: {await state.get_state()}")
        await show_month_control_test_summary_from_callback(
            callback=callback,
            state=state,
            target_state=summary_state,
            callback_prefix=summary_callback_prefix,
            back_keyboard_func=back_keyboard_func,
            items_per_page=items_per_page_summary,
            caption="📊 Сводка по сильным и слабым темам контрольного теста",
            premium_check=premium_check
        )

    # Обработчики пагинации для детальной статистики микротем контрольного теста
    @router.callback_query(detailed_state, F.data.startswith(f"{detailed_callback_prefix}_"))
    async def handle_month_control_detailed_microtopics_pagination(callback: CallbackQuery, state: FSMContext):
        logging.info(f"ВЫЗОВ: handle_month_control_detailed_microtopics_pagination | КОЛБЭК: {callback.data} | СОСТОЯНИЕ: {await state.get_state()}")
        await handle_microtopics_pagination_universal(
            callback=callback,
            state=state,
            callback_prefix=detailed_callback_prefix,
            display_mode="detailed",
            role="student"
        )

    # Обработчики пагинации для сводки микротем контрольного теста
    @router.callback_query(summary_state, F.data.startswith(f"{summary_callback_prefix}_"))
    async def handle_month_control_summary_microtopics_pagination(callback: CallbackQuery, state: FSMContext):
        logging.info(f"ВЫЗОВ: handle_month_control_summary_microtopics_pagination | КОЛБЭК: {callback.data} | СОСТОЯНИЕ: {await state.get_state()}")
        await handle_microtopics_pagination_universal(
            callback=callback,
            state=state,
            callback_prefix=summary_callback_prefix,
            display_mode="summary",
            role="student"
        )

    # Универсальный обработчик возврата из изображений микротем контрольного теста (с фильтрацией по состояниям)
    @router.callback_query(detailed_state, F.data == "back_from_microtopics_image")
    async def back_from_month_control_detailed_microtopics_image(callback: CallbackQuery, state: FSMContext):
        """Возврат из детальной статистики микротем контрольного теста"""
        logging.info(f"ВЫЗОВ: back_from_month_control_detailed_microtopics_image | КОЛБЭК: {callback.data} | СОСТОЯНИЕ: {await state.get_state()}")
        await back_from_month_control_test_microtopics_to_result(callback, state)

    @router.callback_query(summary_state, F.data == "back_from_microtopics_image")
    async def back_from_month_control_summary_microtopics_image(callback: CallbackQuery, state: FSMContext):
        """Возврат из сводки микротем контрольного теста"""
        logging.info(f"ВЫЗОВ: back_from_month_control_summary_microtopics_image | КОЛБЭК: {callback.data} | СОСТОЯНИЕ: {await state.get_state()}")
        await back_from_month_control_test_microtopics_to_result(callback, state)

    # Универсальный обработчик для контрольного теста месяца (регистрируется в каждом роутере)
    @router.callback_query(F.data == "back_to_month_control_result")
    async def universal_back_to_month_control_result(callback: CallbackQuery, state: FSMContext):
        """Универсальный обработчик для всех ролей с проверкой состояния внутри (контрольный тест месяца)"""
        current_state = await state.get_state()

        # Определяем роль по состоянию
        role = None
        target_state_class = None

        if str(current_state).startswith('CuratorTestsStatisticsStates:'):
            role = "curator"
            from curator.states.states_tests import CuratorTestsStatisticsStates
            target_state_class = CuratorTestsStatisticsStates
        elif str(current_state).startswith('TeacherTestsStatisticsStates:'):
            role = "teacher"
            from teacher.states.states_tests import TeacherTestsStatisticsStates
            target_state_class = TeacherTestsStatisticsStates
        elif str(current_state).startswith('ManagerTestsStatisticsStates:'):
            role = "manager"
            from manager.states.states_tests import ManagerTestsStatisticsStates
            target_state_class = ManagerTestsStatisticsStates
        elif str(current_state).startswith('StudentTestsStates:'):
            # Для студентов используем их собственный обработчик возврата
            await back_from_month_control_test_microtopics_to_result(callback, state)
            return
        else:
            # Если не одна из поддерживаемых ролей - игнорируем
            return

        logging.info(f"🔍 УНИВЕРСАЛЬНЫЙ ОБРАБОТЧИК КОНТРОЛЬНОГО ТЕСТА СРАБОТАЛ! ROLE: {role} | USER: {callback.from_user.id} | CURRENT_STATE: {current_state}")

        # Вызываем основную логику
        data = await state.get_data()
        group_id = data.get('month_control_group_id')
        month_test_id = data.get('month_control_month_test_id')
        student_id = data.get('month_control_student_id')

        if all([group_id, month_test_id, student_id]):
            # Для студента используем существующую логику
            if role == "student":
                try:
                    from common.student_tests.transitions import handle_month_control_result
                    await handle_month_control_result(callback, state)
                    return
                except Exception as e:
                    logging.error(f"❌ Ошибка в обработчике студента: {e}")

            # Для остальных ролей используем универсальную функцию
            try:
                # Сначала пытаемся удалить сообщение с изображением
                try:
                    await callback.message.delete()
                except Exception as e:
                    logging.info(f"Сообщение уже удалено или не может быть удалено: {e}")

                from common.microtopics.universal_adapter import show_month_control_test_statistics_like_student
                await show_month_control_test_statistics_like_student(callback, state, group_id, month_test_id, student_id)

                # Устанавливаем правильное состояние для соответствующей роли
                await state.set_state(target_state_class.month_control_result)

                # Сохраняем данные для навигации
                await state.update_data(
                    month_control_group_id=group_id,
                    month_control_month_test_id=month_test_id,
                    month_control_student_id=student_id
                )

            except Exception as e:
                logging.error(f"❌ Ошибка в универсальном обработчике контрольного теста: {e}")
                try:
                    await callback.message.answer("❌ Ошибка при получении данных")
                except Exception:
                    await callback.answer("❌ Ошибка при получении данных")
        else:
            await callback.answer("❌ Ошибка: данные не найдены")


def register_course_entry_test_microtopics_handlers(
    router: Router,
    states_group,
    detailed_callback_prefix: str,
    summary_callback_prefix: str,
    detailed_state,
    summary_state,
    result_state,
    back_keyboard_func,
    items_per_page_detailed: int = 15,
    items_per_page_summary: int = 15,
    premium_check: bool = False
):
    """
    Регистрация универсальных обработчиков микротем для входных тестов курса

    Args:
        router: Роутер для регистрации обработчиков
        states_group: Группа состояний
        detailed_callback_prefix: Префикс для детальной пагинации (например: "curator_course_entry_page")
        summary_callback_prefix: Префикс для пагинации сводки (например: "curator_course_entry_summary_page")
        detailed_state: Состояние детальной статистики
        summary_state: Состояние сводки
        result_state: Состояние результата теста
        back_keyboard_func: Функция для кнопки "Назад"
        items_per_page_detailed: Элементов на страницу для детальной статистики
        items_per_page_summary: Элементов на страницу для сводки
        premium_check: Нужна ли проверка премиума
    """

    # Обработчики пагинации для детальной статистики микротем входного теста курса
    @router.callback_query(detailed_state, F.data.startswith(f"{detailed_callback_prefix}_"))
    async def handle_course_entry_detailed_microtopics_pagination(callback: CallbackQuery, state: FSMContext):
        logging.info(f"ВЫЗОВ: handle_course_entry_detailed_microtopics_pagination | КОЛБЭК: {callback.data} | СОСТОЯНИЕ: {await state.get_state()}")
        await handle_microtopics_pagination_universal(
            callback=callback,
            state=state,
            callback_prefix=detailed_callback_prefix,
            display_mode="detailed",
            role="curator"  # Роль не важна для пагинации
        )

    # Обработчики пагинации для сводки микротем входного теста курса
    @router.callback_query(summary_state, F.data.startswith(f"{summary_callback_prefix}_"))
    async def handle_course_entry_summary_microtopics_pagination(callback: CallbackQuery, state: FSMContext):
        logging.info(f"ВЫЗОВ: handle_course_entry_summary_microtopics_pagination | КОЛБЭК: {callback.data} | СОСТОЯНИЕ: {await state.get_state()}")
        await handle_microtopics_pagination_universal(
            callback=callback,
            state=state,
            callback_prefix=summary_callback_prefix,
            display_mode="summary",
            role="curator"  # Роль не важна для пагинации
        )

    # Универсальный обработчик возврата из изображений микротем входного теста курса (с фильтрацией по состояниям)
    @router.callback_query(detailed_state, F.data == "back_from_microtopics_image")
    async def back_from_course_entry_detailed_microtopics_image(callback: CallbackQuery, state: FSMContext):
        """Возврат из детальной статистики микротем входного теста курса"""
        current_state = await state.get_state()
        logging.info(f"🔙 ОБРАБОТЧИК ВОЗВРАТА DETAILED ВХОДНОГО ТЕСТА КУРСА | USER: {callback.from_user.id} | STATE: {current_state} | CALLBACK: {callback.data}")
        logging.info(f"🔙 DETAILED_STATE ФИЛЬТР: {detailed_state}")
        logging.info(f"🔙 СРАВНЕНИЕ: current_state == detailed_state: {current_state == detailed_state}")
        logging.info(f"🔙 СРАВНЕНИЕ: str(current_state) == str(detailed_state): {str(current_state) == str(detailed_state)}")
        from common.tests_statistics.handlers import back_from_course_entry_microtopics_to_result
        await back_from_course_entry_microtopics_to_result(callback, state)

    @router.callback_query(summary_state, F.data == "back_from_microtopics_image")
    async def back_from_course_entry_summary_microtopics_image(callback: CallbackQuery, state: FSMContext):
        """Возврат из сводки микротем входного теста курса"""
        current_state = await state.get_state()
        logging.info(f"🔙 ОБРАБОТЧИК ВОЗВРАТА SUMMARY ВХОДНОГО ТЕСТА КУРСА | USER: {callback.from_user.id} | STATE: {current_state} | CALLBACK: {callback.data}")
        logging.info(f"🔙 SUMMARY_STATE ФИЛЬТР: {summary_state}")
        logging.info(f"🔙 СРАВНЕНИЕ: current_state == summary_state: {current_state == summary_state}")
        logging.info(f"🔙 СРАВНЕНИЕ: str(current_state) == str(summary_state): {str(current_state) == str(summary_state)}")
        from common.tests_statistics.handlers import back_from_course_entry_microtopics_to_result
        await back_from_course_entry_microtopics_to_result(callback, state)

    # Универсальный обработчик для отладки - ловит все back_from_microtopics_image независимо от состояния
    @router.callback_query(F.data == "back_from_microtopics_image")
    async def debug_back_from_microtopics_image(callback: CallbackQuery, state: FSMContext):
        """Отладочный обработчик для всех back_from_microtopics_image"""
        current_state = await state.get_state()
        logging.error(f"🚨 УНИВЕРСАЛЬНЫЙ ОБРАБОТЧИК ВОЗВРАТА | USER: {callback.from_user.id} | STATE: {current_state} | CALLBACK: {callback.data}")
        logging.error(f"🚨 DETAILED_STATE: {detailed_state}")
        logging.error(f"🚨 SUMMARY_STATE: {summary_state}")
        logging.error(f"🚨 СОСТОЯНИЕ СОВПАДАЕТ С DETAILED: {current_state == detailed_state}")
        logging.error(f"🚨 СОСТОЯНИЕ СОВПАДАЕТ С SUMMARY: {current_state == summary_state}")

        # Если состояние не совпадает ни с одним из фильтров, попробуем все равно вызвать функцию возврата
        from common.tests_statistics.handlers import back_from_course_entry_microtopics_to_result
        await back_from_course_entry_microtopics_to_result(callback, state)
